import { useTranslation } from "react-i18next";
import { authService } from "../../services/authService";

/** Renders for deactivated user */
function DeactivatedUser() {
  const { t } = useTranslation();
  document.title = t("app.DeactivatedUser");

  // Debug logging
  console.log("DeactivatedUser component rendered");
  console.log("Translation message:", t("Errors.DeactivatedUserMessage"));
  console.log("Current user:", authService.getCurrentUser());

  // on back button click redirect user to landing screen
  const onNavigate = () => {
    authService.logout();
  };

  return (
    <div className='section-background-image' style={{ height: "100vh" }}>
      <div className='section-bg-content'>
        <div className='content'>{t("Errors.DeactivatedUserMessage")}</div>

        <div className='button-group'>
          <button className={"btn app-btn-secondary"} onClick={onNavigate}>
            {t("Common.Logout")}
          </button>
        </div>
      </div>
    </div>
  );
}

export default DeactivatedUser;
